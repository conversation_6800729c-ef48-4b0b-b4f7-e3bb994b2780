import React, { useState, useEffect } from "react";
import LogManager from "../LogManager";

// Updated CSS styles embedded in component
const styles = `
.ruda-container {
    width: 100%;
    height: 100vh;
    font-family: Arial, sans-serif;
    font-size: 12px;
    display: flex;
    flex-direction: column;
  }

  .ruda-content {
    flex: 1;
    overflow: auto;
  }

  .ruda-header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #1e3a5f;
    color: white;
    padding: 12px 20px;
    margin-bottom: 0;
  }

  .ruda-title {
    font-weight: bold;
    font-size: 20px;
    margin: 0;
  }

  .ruda-logo {
    color: #c0c0c0;
    font-size: 16px;
    font-weight: bold;
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .ruda-table {
    border-collapse: collapse;
    width: 100%;
    min-width: 1600px;
    border-radius: 0px;
    overflow: hidden;
  }

  .ruda-table th,
  .ruda-table td {
    border: 1px solid #ddd;
    text-align: left;
    padding: 4px 8px;
    font-size: 11px;
    white-space: nowrap;
  }

  .ruda-table th {
    background-color: #f5f5f5;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 10;
  }

  .ruda-phase-row {
    background-color: #e8f4fd;
    font-weight: bold;
    cursor: pointer;
  }

  .ruda-phase-row:hover {
    background-color: #d1e7dd;
  }

  .ruda-phase-header {
    background-color: #4a90e2 !important;
    color: white !important;
    font-weight: bold;
    padding: 8px !important;
  }

  .ruda-package-row {
    background-color: #f8f9fa;
    font-weight: bold;
    cursor: pointer;
  }

  .ruda-package-row:hover {
    background-color: #e9ecef;
  }

  .ruda-subpackage-row {
    background-color: #fff3cd;
    cursor: pointer;
  }

  .ruda-subpackage-row:hover {
    background-color: #ffeaa7;
  }

  .ruda-subsubpackage-row {
    background-color: #d4edda;
    cursor: pointer;
  }

  .ruda-subsubpackage-row:hover {
    background-color: #c3e6cb;
  }

  .ruda-activity-row {
    background-color: #f8d7da;
    cursor: pointer;
  }

  .ruda-activity-row:hover {
    background-color: #f5c6cb;
  }

  .ruda-reach-row {
    background-color: #e2e3e5;
    cursor: pointer;
  }

  .ruda-reach-row:hover {
    background-color: #d6d8db;
  }

  .ruda-material-row {
    background-color: #fff;
    cursor: pointer;
  }

  .ruda-material-row:hover {
    background-color: #f8f9fa;
  }

  .ruda-item-row {
    background-color: #fff;
    cursor: pointer;
  }

  .ruda-item-row:hover {
    background-color: #f8f9fa;
  }

  .ruda-timeline-cell {
    width: 20px;
    height: 20px;
    text-align: center;
    padding: 2px !important;
    font-size: 10px;
  }

  .ruda-timeline-active {
    background-color: #28a745 !important;
    color: white;
  }

  .ruda-timeline-inactive {
    background-color: #f8f9fa;
  }

  .right {
    text-align: right;
  }

  .ruda-selected-row {
    background-color: #007bff !important;
    color: white !important;
  }

  .ruda-selected-row:hover {
    background-color: #0056b3 !important;
  }

  .ruda-month-header {
    writing-mode: vertical-rl;
    text-orientation: mixed;
    padding: 4px 2px !important;
    font-size: 10px;
    min-width: 20px;
    max-width: 20px;
  }
`;

const RUDADevelopmentPlan = () => {
  const [expandedPhases, setExpandedPhases] = useState(new Set([0]));
  const [expandedPackages, setExpandedPackages] = useState(new Set());
  const [expandedSubpackages, setExpandedSubpackages] = useState(new Set());
  const [expandedSubsubpackages, setExpandedSubsubpackages] = useState(
    new Set()
  );
  const [expandedReaches, setExpandedReaches] = useState(new Set());
  const [selectedItem, setSelectedItem] = useState(null);
  const [showLog, setShowLog] = useState(false);
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch data from backend
  useEffect(() => {
    const fetchGanttData = async () => {
      try {
        setLoading(true);
        const response = await fetch("http://localhost:5000/api/ganttcrud");
        const result = await response.json();

        if (result.success) {
          setData(result.data);
        } else {
          console.error("Failed to fetch Gantt data:", result.error);
          // Fallback to empty array if API fails
          setData([]);
        }
      } catch (error) {
        console.error("Error fetching Gantt data:", error);
        // Fallback to empty array if API fails
        setData([]);
      } finally {
        setLoading(false);
      }
    };

    fetchGanttData();
  }, []);

  const months = [
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
    "Jan",
    "Feb",
    "Mar",
  ];

  const togglePhase = (phaseIndex) => {
    const newSet = new Set(expandedPhases);
    newSet.has(phaseIndex) ? newSet.delete(phaseIndex) : newSet.add(phaseIndex);
    setExpandedPhases(newSet);
  };

  const togglePackage = (packageKey) => {
    const newSet = new Set(expandedPackages);
    newSet.has(packageKey) ? newSet.delete(packageKey) : newSet.add(packageKey);
    setExpandedPackages(newSet);
  };

  const toggleSubpackage = (subpackageKey) => {
    const newSet = new Set(expandedSubpackages);
    newSet.has(subpackageKey)
      ? newSet.delete(subpackageKey)
      : newSet.add(subpackageKey);
    setExpandedSubpackages(newSet);
  };

  const toggleSubsubpackage = (subsubpackageKey) => {
    const newSet = new Set(expandedSubsubpackages);
    newSet.has(subsubpackageKey)
      ? newSet.delete(subsubpackageKey)
      : newSet.add(subsubpackageKey);
    setExpandedSubsubpackages(newSet);
  };

  const toggleReach = (reachKey) => {
    const newSet = new Set(expandedReaches);
    newSet.has(reachKey) ? newSet.delete(reachKey) : newSet.add(reachKey);
    setExpandedReaches(newSet);
  };

  const handleItemClick = async (item, type = "item") => {
    // Only set timeline for leaf items that have timeline data
    if (item.timeline && Array.isArray(item.timeline)) {
      setSelectedItem(item);

      // Log the view action for demonstration
      try {
        await fetch("http://localhost:5000/api/ganttcrud/log-view", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            itemId: item.name?.replace(/\s+/g, "-").toLowerCase() || "unknown",
            itemName: item.name || "Unknown Item",
            action: "VIEW",
          }),
        });
      } catch (error) {
        console.error("Failed to log item view:", error);
      }
    }
  };

  if (loading) {
    return (
      <div className="ruda-container">
        <style>{styles}</style>
        <div className="ruda-header-container">
          <h1 className="ruda-title">RUDA Development Plan</h1>
          <div className="ruda-logo">RUDA</div>
        </div>
        <div style={{ padding: "20px", textAlign: "center" }}>
          Loading Gantt data...
        </div>
      </div>
    );
  }

  return (
    <div className="ruda-container">
      <style>{styles}</style>
      <div className="ruda-header-container">
        <h1 className="ruda-title">RUDA Development Plan</h1>
        <div className="ruda-logo" onClick={() => setShowLog(true)}>
          VIEW LOG
        </div>
      </div>
      <div className="ruda-content">
        <table className="ruda-table">
          <thead>
            <tr>
              <th style={{ minWidth: "300px" }}>PHASES / PACKAGES</th>
              <th style={{ minWidth: "80px" }}>Amount (PKR, M)</th>
              <th style={{ minWidth: "80px" }}>Duration (Days)</th>
              <th style={{ minWidth: "80px" }}>Schedule %</th>
              <th style={{ minWidth: "80px" }}>Performance %</th>
              <th style={{ minWidth: "80px" }}>Planned Value (PKR, M)</th>
              <th style={{ minWidth: "80px" }}>Earned Value (PKR, M)</th>
              <th style={{ minWidth: "80px" }}>Actual Start</th>
              <th style={{ minWidth: "80px" }}>Actual Finish</th>
              <th
                colSpan={12}
                style={{
                  textAlign: "center",
                  backgroundColor: "#4a90e2",
                  color: "white",
                }}
              >
                FY 24-25
              </th>
              <th
                colSpan={12}
                style={{
                  textAlign: "center",
                  backgroundColor: "#4a90e2",
                  color: "white",
                }}
              >
                FY 25-26
              </th>
              <th
                colSpan={12}
                style={{
                  textAlign: "center",
                  backgroundColor: "#4a90e2",
                  color: "white",
                }}
              >
                FY 26-27
              </th>
              <th
                colSpan={12}
                style={{
                  textAlign: "center",
                  backgroundColor: "#4a90e2",
                  color: "white",
                }}
              >
                FY 27-28
              </th>
              <th
                colSpan={12}
                style={{
                  textAlign: "center",
                  backgroundColor: "#4a90e2",
                  color: "white",
                }}
              >
                FY 28-29
              </th>
            </tr>
            <tr>
              <th></th>
              <th></th>
              <th></th>
              <th></th>
              <th></th>
              <th></th>
              <th></th>
              <th></th>
              <th></th>
              {months.map((month, index) => (
                <th key={index} className="ruda-month-header">
                  {month}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.map((phase, phaseIndex) => (
              <React.Fragment key={phaseIndex}>
                <tr
                  className="ruda-phase-row"
                  onClick={() => togglePhase(phaseIndex)}
                >
                  <td className="ruda-phase-header">
                    {phase.phase} {expandedPhases.has(phaseIndex) ? "▲" : "▼"}
                  </td>
                  <td className="ruda-phase-header right">{phase.amount}</td>
                  <td className="ruda-phase-header right">-</td>
                  <td className="ruda-phase-header right">-</td>
                  <td className="ruda-phase-header right">-</td>
                  <td className="ruda-phase-header right">-</td>
                  <td className="ruda-phase-header right">-</td>
                  <td className="ruda-phase-header right">-</td>
                  <td className="ruda-phase-header right">-</td>
                  {months.map((_, monthIndex) => (
                    <td key={monthIndex} className="ruda-phase-header"></td>
                  ))}
                </tr>
                {expandedPhases.has(phaseIndex) && (
                  <>
                    {/* Render simple items (Phase 1 style) */}
                    {phase.items &&
                      phase.items.map((item, itemIndex) => (
                        <tr
                          key={`item-${itemIndex}`}
                          className={`ruda-item-row ${
                            selectedItem === item ? "ruda-selected-row" : ""
                          }`}
                          onClick={() => handleItemClick(item, "item")}
                        >
                          <td style={{ paddingLeft: "20px" }}>{item.name}</td>
                          <td className="right">{item.amount}</td>
                          <td className="right">-</td>
                          <td className="right">-</td>
                          <td className="right">-</td>
                          <td className="right">-</td>
                          <td className="right">-</td>
                          <td className="right">-</td>
                          <td className="right">-</td>
                          {item.timeline.map((value, timeIndex) => (
                            <td
                              key={timeIndex}
                              className={`ruda-timeline-cell ${
                                value === 1
                                  ? "ruda-timeline-active"
                                  : "ruda-timeline-inactive"
                              }`}
                            >
                              {value === 1 ? "●" : ""}
                            </td>
                          ))}
                        </tr>
                      ))}

                    {/* Render packages (Phase 2+ style) */}
                    {phase.packages &&
                      phase.packages.map((pkg, packageIndex) => {
                        const packageKey = `${phaseIndex}-${packageIndex}`;
                        return (
                          <React.Fragment key={packageKey}>
                            <tr
                              className={`ruda-package-row ${
                                selectedItem === pkg ? "ruda-selected-row" : ""
                              }`}
                              onClick={() => {
                                togglePackage(packageKey);
                                handleItemClick(pkg, "package");
                              }}
                            >
                              <td style={{ paddingLeft: "20px" }}>
                                {pkg.name}{" "}
                                {expandedPackages.has(packageKey) ? "▲" : "▼"}
                              </td>
                              <td className="right">{pkg.budgetedCost}</td>
                              <td className="right">-</td>
                              <td className="right">{pkg.scheduleComplete}</td>
                              <td className="right">
                                {pkg.performanceComplete}
                              </td>
                              <td className="right">{pkg.plannedValue}</td>
                              <td className="right">{pkg.earnedValue}</td>
                              <td className="right">{pkg.actualStart}</td>
                              <td className="right">{pkg.actualFinish}</td>
                              {pkg.timeline.map((value, timeIndex) => (
                                <td
                                  key={timeIndex}
                                  className={`ruda-timeline-cell ${
                                    value === 1
                                      ? "ruda-timeline-active"
                                      : "ruda-timeline-inactive"
                                  }`}
                                >
                                  {value === 1 ? "●" : ""}
                                </td>
                              ))}
                            </tr>
                            {expandedPackages.has(packageKey) &&
                              pkg.subpackages &&
                              pkg.subpackages.map((subpkg, subpackageIndex) => {
                                const subpackageKey = `${packageKey}-${subpackageIndex}`;
                                return (
                                  <React.Fragment key={subpackageKey}>
                                    <tr
                                      className={`ruda-subpackage-row ${
                                        selectedItem === subpkg
                                          ? "ruda-selected-row"
                                          : ""
                                      }`}
                                      onClick={() => {
                                        toggleSubpackage(subpackageKey);
                                        handleItemClick(subpkg, "subpackage");
                                      }}
                                    >
                                      <td style={{ paddingLeft: "40px" }}>
                                        {subpkg.name}{" "}
                                        {expandedSubpackages.has(subpackageKey)
                                          ? "▲"
                                          : "▼"}
                                      </td>
                                      <td className="right">
                                        {subpkg.budgetedCost}
                                      </td>
                                      <td className="right">
                                        {subpkg.duration}
                                      </td>
                                      <td className="right">
                                        {subpkg.scheduleComplete}
                                      </td>
                                      <td className="right">
                                        {subpkg.performanceComplete}
                                      </td>
                                      <td className="right">
                                        {subpkg.plannedValue}
                                      </td>
                                      <td className="right">
                                        {subpkg.earnedValue}
                                      </td>
                                      <td className="right">
                                        {subpkg.actualStart}
                                      </td>
                                      <td className="right">
                                        {subpkg.actualFinish}
                                      </td>
                                      {subpkg.timeline.map(
                                        (value, timeIndex) => (
                                          <td
                                            key={timeIndex}
                                            className={`ruda-timeline-cell ${
                                              value === 1
                                                ? "ruda-timeline-active"
                                                : "ruda-timeline-inactive"
                                            }`}
                                          >
                                            {value === 1 ? "●" : ""}
                                          </td>
                                        )
                                      )}
                                    </tr>
                                    {expandedSubpackages.has(subpackageKey) && (
                                      <>
                                        {/* Render subsubpackages */}
                                        {subpkg.subsubpackages &&
                                          subpkg.subsubpackages.map(
                                            (subsubpkg, subsubpackageIndex) => {
                                              const subsubpackageKey = `${subpackageKey}-${subsubpackageIndex}`;
                                              return (
                                                <React.Fragment
                                                  key={subsubpackageKey}
                                                >
                                                  <tr
                                                    className={`ruda-subsubpackage-row ${
                                                      selectedItem === subsubpkg
                                                        ? "ruda-selected-row"
                                                        : ""
                                                    }`}
                                                    onClick={() => {
                                                      toggleSubsubpackage(
                                                        subsubpackageKey
                                                      );
                                                      handleItemClick(
                                                        subsubpkg,
                                                        "subsubpackage"
                                                      );
                                                    }}
                                                  >
                                                    <td
                                                      style={{
                                                        paddingLeft: "60px",
                                                      }}
                                                    >
                                                      {subsubpkg.name}{" "}
                                                      {expandedSubsubpackages.has(
                                                        subsubpackageKey
                                                      )
                                                        ? "▲"
                                                        : "▼"}
                                                    </td>
                                                    <td className="right">
                                                      {subsubpkg.budgetedCost}
                                                    </td>
                                                    <td className="right">
                                                      {subsubpkg.duration}
                                                    </td>
                                                    <td className="right">
                                                      {
                                                        subsubpkg.scheduleComplete
                                                      }
                                                    </td>
                                                    <td className="right">
                                                      {
                                                        subsubpkg.performanceComplete
                                                      }
                                                    </td>
                                                    <td className="right">
                                                      {subsubpkg.plannedValue}
                                                    </td>
                                                    <td className="right">
                                                      {subsubpkg.earnedValue}
                                                    </td>
                                                    <td className="right">
                                                      {subsubpkg.actualStart}
                                                    </td>
                                                    <td className="right">
                                                      {subsubpkg.actualFinish}
                                                    </td>
                                                    {subsubpkg.timeline.map(
                                                      (value, timeIndex) => (
                                                        <td
                                                          key={timeIndex}
                                                          className={`ruda-timeline-cell ${
                                                            value === 1
                                                              ? "ruda-timeline-active"
                                                              : "ruda-timeline-inactive"
                                                          }`}
                                                        >
                                                          {value === 1
                                                            ? "●"
                                                            : ""}
                                                        </td>
                                                      )
                                                    )}
                                                  </tr>
                                                  {expandedSubsubpackages.has(
                                                    subsubpackageKey
                                                  ) &&
                                                    subsubpkg.activities &&
                                                    subsubpkg.activities.map(
                                                      (
                                                        activity,
                                                        activityIndex
                                                      ) => (
                                                        <tr
                                                          key={`${subsubpackageKey}-activity-${activityIndex}`}
                                                          className={`ruda-activity-row ${
                                                            selectedItem ===
                                                            activity
                                                              ? "ruda-selected-row"
                                                              : ""
                                                          }`}
                                                          onClick={() =>
                                                            handleItemClick(
                                                              activity,
                                                              "activity"
                                                            )
                                                          }
                                                        >
                                                          <td
                                                            style={{
                                                              paddingLeft:
                                                                "80px",
                                                            }}
                                                          >
                                                            {activity.name}
                                                          </td>
                                                          <td className="right">
                                                            {
                                                              activity.plannedValue
                                                            }
                                                          </td>
                                                          <td className="right">
                                                            {activity.duration}
                                                          </td>
                                                          <td className="right">
                                                            {
                                                              activity.scheduleComplete
                                                            }
                                                          </td>
                                                          <td className="right">
                                                            {
                                                              activity.performanceComplete
                                                            }
                                                          </td>
                                                          <td className="right">
                                                            {
                                                              activity.plannedValue
                                                            }
                                                          </td>
                                                          <td className="right">
                                                            {
                                                              activity.earnedValue
                                                            }
                                                          </td>
                                                          <td className="right">
                                                            {
                                                              activity.actualStart
                                                            }
                                                          </td>
                                                          <td className="right">
                                                            {
                                                              activity.actualFinish
                                                            }
                                                          </td>
                                                          {activity.timeline.map(
                                                            (
                                                              value,
                                                              timeIndex
                                                            ) => (
                                                              <td
                                                                key={timeIndex}
                                                                className={`ruda-timeline-cell ${
                                                                  value === 1
                                                                    ? "ruda-timeline-active"
                                                                    : "ruda-timeline-inactive"
                                                                }`}
                                                              >
                                                                {value === 1
                                                                  ? "●"
                                                                  : ""}
                                                              </td>
                                                            )
                                                          )}
                                                        </tr>
                                                      )
                                                    )}
                                                </React.Fragment>
                                              );
                                            }
                                          )}

                                        {/* Render direct activities of subpackage */}
                                        {subpkg.activities &&
                                          subpkg.activities.map(
                                            (activity, activityIndex) => (
                                              <tr
                                                key={`${subpackageKey}-activity-${activityIndex}`}
                                                className={`ruda-activity-row ${
                                                  selectedItem === activity
                                                    ? "ruda-selected-row"
                                                    : ""
                                                }`}
                                                onClick={() =>
                                                  handleItemClick(
                                                    activity,
                                                    "activity"
                                                  )
                                                }
                                              >
                                                <td
                                                  style={{
                                                    paddingLeft: "60px",
                                                  }}
                                                >
                                                  {activity.name}
                                                </td>
                                                <td className="right">
                                                  {activity.plannedValue}
                                                </td>
                                                {activity.timeline.map(
                                                  (value, timeIndex) => (
                                                    <td
                                                      key={timeIndex}
                                                      className={`ruda-timeline-cell ${
                                                        value === 1
                                                          ? "ruda-timeline-active"
                                                          : "ruda-timeline-inactive"
                                                      }`}
                                                    >
                                                      {value === 1 ? "●" : ""}
                                                    </td>
                                                  )
                                                )}
                                              </tr>
                                            )
                                          )}
                                      </>
                                    )}
                                  </React.Fragment>
                                );
                              })}
                          </React.Fragment>
                        );
                      })}
                  </>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
      {showLog && (
        <LogManager logType="gantt" onClose={() => setShowLog(false)} />
      )}
    </div>
  );
};

export default RUDADevelopmentPlan;
